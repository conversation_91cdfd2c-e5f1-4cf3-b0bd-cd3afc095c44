@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens for Chenesa.io water management theme */
  --background: oklch(1 0 0); /* White background */
  --foreground: oklch(0.3 0 0); /* Dark gray text #374151 */
  --card: oklch(0.98 0.02 142); /* Light green card background #f0fdf4 */
  --card-foreground: oklch(0.3 0 0); /* Dark gray card text */
  --popover: oklch(1 0 0); /* White popover */
  --popover-foreground: oklch(0.3 0 0); /* Dark gray popover text */
  --primary: oklch(0.45 0.15 142); /* Green primary #15803d */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.98 0.02 142); /* Light green secondary */
  --secondary-foreground: oklch(0.3 0 0); /* Dark gray secondary text */
  --muted: oklch(0.96 0 0); /* Light gray muted */
  --muted-foreground: oklch(0.5 0 0); /* Medium gray muted text */
  --accent: oklch(0.7 0.15 120); /* Lime green accent #84cc16 */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0.5 0.22 15); /* Red destructive */
  --destructive-foreground: oklch(1 0 0); /* White text on destructive */
  --border: oklch(0.9 0 0); /* Light gray border */
  --input: oklch(0.98 0.02 142); /* Light green input background */
  --ring: oklch(0.45 0.15 142 / 0.5); /* Green focus ring */
  --chart-1: oklch(0.45 0.15 142); /* Green chart color */
  --chart-2: oklch(0.7 0.15 120); /* Lime green chart color */
  --chart-3: oklch(0.98 0.02 142); /* Light green chart color */
  --chart-4: oklch(0.3 0 0); /* Dark gray chart color */
  --chart-5: oklch(0.5 0 0); /* Medium gray chart color */
  --radius: 0.5rem;
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.3 0 0);
  --sidebar-primary: oklch(0.45 0.15 142);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.7 0.15 120);
  --sidebar-accent-foreground: oklch(0.3 0 0);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: oklch(0.45 0.15 142 / 0.5);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
