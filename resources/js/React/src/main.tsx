import {StrictMode} from 'react';
import {createRoot} from 'react-dom/client';
import {Toaster} from 'react-hot-toast';
import App from './App.tsx';
import {AuthProvider} from './contexts/AuthContext';
import './index.css';

createRoot(document.getElementById('root')!).render(
    <StrictMode>
        <AuthProvider>
            <App/>
            <Toaster position="top-right"/>
        </AuthProvider>
    </StrictMode>
);
