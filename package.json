{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.0.11"}, "dependencies": {"@supabase/supabase-js": "^2.49.1", "date-fns": "^4.1.0", "lucide-react": "^0.477.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.2.0", "recharts": "^2.15.1"}}